import time
import random
import math
from typing import Optional, Dict, Any, List
from collections import deque, Counter
from .base_strategy import BaseBettingStrategy, BetInfo, BetResult
from .persistent_storage import PersistentOwoPatternAnalyzer

class OwoPatternAnalyzer(PersistentOwoPatternAnalyzer, BaseBettingStrategy):
    """
    Specialized strategy for analyzing owo bot patterns:
    1. Time-based pattern analysis
    2. User behavior correlation
    3. Pseudo-random number generator analysis
    4. Statistical anomaly detection
    5. Adaptive learning from owo responses
    """
    
    def __init__(self, initial_bankroll: int):
        super().__init__(initial_bankroll)
        
        # Owo-specific pattern tracking
        self.owo_results = deque(maxlen=200)  # Store detailed owo results
        self.time_patterns = {}  # Time-based patterns
        self.user_correlation = {}  # Other users' results correlation
        
        # Statistical analysis
        self.chi_square_threshold = 3.84  # 95% confidence
        self.runs_test_data = deque(maxlen=50)
        
        # Adaptive parameters
        self.base_bet = 50  # Conservative base bet
        self.confidence_multiplier = 1.0
        self.pattern_strength = 0.0
        
        # Anti-detection measures
        self.bet_randomization = True
        self.timing_randomization = True
        self.pattern_masking = True
        
    def calculate_next_bet(self) -> Optional[BetInfo]:
        """Calculate bet using owo-specific analysis"""
        if self.should_stop_betting():
            return None
        
        # Analyze current owo patterns
        analysis = self._analyze_owo_patterns()
        
        if not analysis:
            # Use conservative random betting when no analysis available
            if random.random() < 0.3:  # Only bet 30% of the time when uncertain
                return self._conservative_bet()
            return None

        if analysis['confidence'] < 0.55:
            # Use conservative random betting when no clear pattern
            if random.random() < 0.3:  # Only bet 30% of the time when uncertain
                return self._conservative_bet()
            return None
        
        # Calculate bet based on pattern strength
        bet_amount = self._calculate_pattern_bet(analysis)
        side = analysis['predicted_side']
        
        # Apply anti-detection randomization
        if self.pattern_masking and random.random() < 0.2:
            side = 't' if side == 'h' else 'h'  # 20% chance to bet opposite
            bet_amount = int(bet_amount * 0.7)  # Reduce bet when masking
        
        print(f"🎯 Owo Pattern Analysis:")
        print(f"   Pattern: {analysis['pattern_type']}")
        print(f"   Confidence: {analysis['confidence']:.1%}")
        print(f"   Predicted: {side}")
        print(f"   Bet: {bet_amount}")
        
        return BetInfo(
            amount=bet_amount,
            side=side,
            timestamp=time.time()
        )
    
    def _analyze_owo_patterns(self) -> Optional[Dict[str, Any]]:
        """Comprehensive owo pattern analysis"""
        if len(self.owo_results) < 10:
            return None
        
        analyses = []
        
        # Time-based analysis
        time_analysis = self._analyze_time_patterns()
        if time_analysis:
            analyses.append(time_analysis)
        
        # Sequence analysis
        sequence_analysis = self._analyze_sequences()
        if sequence_analysis:
            analyses.append(sequence_analysis)
        
        # Statistical bias detection
        bias_analysis = self._detect_statistical_bias()
        if bias_analysis:
            analyses.append(bias_analysis)
        
        # Runs test for randomness
        runs_analysis = self._runs_test_analysis()
        if runs_analysis:
            analyses.append(runs_analysis)
        
        return self._combine_analyses(analyses)
    
    def _analyze_time_patterns(self) -> Optional[Dict[str, Any]]:
        """Analyze time-based patterns in owo results"""
        current_time = time.time()
        current_minute = int(current_time / 60) % 60
        current_second = int(current_time) % 60
        
        # Check if certain times favor certain outcomes
        time_key = f"{current_minute // 10}_{current_second // 10}"
        
        if time_key in self.time_patterns:
            pattern_data = self.time_patterns[time_key]
            total = sum(pattern_data.values())
            
            if total >= 5:  # Need sufficient data
                h_prob = pattern_data.get('h', 0) / total
                t_prob = pattern_data.get('t', 0) / total
                
                if abs(h_prob - 0.5) > 0.15:  # Significant deviation
                    predicted_side = 'h' if h_prob > 0.5 else 't'
                    confidence = 0.5 + abs(h_prob - 0.5)
                    
                    return {
                        'pattern_type': 'Time-based',
                        'predicted_side': predicted_side,
                        'confidence': min(confidence, 0.75),
                        'weight': 0.2
                    }
        
        return None
    
    def _analyze_sequences(self) -> Optional[Dict[str, Any]]:
        """Analyze common sequences in owo results"""
        if len(self.owo_results) < 8:
            return None
        
        recent_sequence = [r['result'] for r in list(self.owo_results)[-7:]]
        
        # Look for alternating patterns
        if len(recent_sequence) >= 4:
            # Check for HTHT or THTH patterns
            alternating_ht = all(recent_sequence[i] != recent_sequence[i+1] 
                               for i in range(len(recent_sequence)-1))
            
            if alternating_ht and len(recent_sequence) >= 3:
                # Predict continuation of alternating pattern
                last_result = recent_sequence[-1]
                predicted_side = 't' if last_result == 'h' else 'h'
                
                return {
                    'pattern_type': 'Alternating sequence',
                    'predicted_side': predicted_side,
                    'confidence': 0.65,
                    'weight': 0.3
                }
        
        # Look for repeating patterns
        for pattern_length in [2, 3, 4]:
            if len(recent_sequence) >= pattern_length * 2:
                pattern = recent_sequence[-pattern_length:]
                prev_pattern = recent_sequence[-pattern_length*2:-pattern_length]
                
                if pattern == prev_pattern:
                    # Pattern is repeating, predict next in sequence
                    if len(self.owo_results) > pattern_length * 2:
                        next_in_pattern = list(self.owo_results)[-pattern_length*2-1]['result']
                        
                        return {
                            'pattern_type': f'Repeating-{pattern_length}',
                            'predicted_side': next_in_pattern,
                            'confidence': 0.6,
                            'weight': 0.25
                        }
        
        return None
    
    def _detect_statistical_bias(self) -> Optional[Dict[str, Any]]:
        """Detect statistical bias in recent results"""
        if len(self.owo_results) < 20:
            return None
        
        recent_results = [r['result'] for r in list(self.owo_results)[-20:]]
        h_count = recent_results.count('h')
        t_count = recent_results.count('t')
        
        # Chi-square test for bias
        expected = len(recent_results) / 2
        chi_square = ((h_count - expected) ** 2 / expected + 
                     (t_count - expected) ** 2 / expected)
        
        if chi_square > self.chi_square_threshold:
            # Significant bias detected
            if h_count < t_count:
                # Heads is underrepresented
                return {
                    'pattern_type': 'Statistical bias (heads deficit)',
                    'predicted_side': 'h',
                    'confidence': min(0.7, 0.5 + (t_count - h_count) / 40),
                    'weight': 0.3
                }
            else:
                # Tails is underrepresented
                return {
                    'pattern_type': 'Statistical bias (tails deficit)',
                    'predicted_side': 't',
                    'confidence': min(0.7, 0.5 + (h_count - t_count) / 40),
                    'weight': 0.3
                }
        
        return None
    
    def _runs_test_analysis(self) -> Optional[Dict[str, Any]]:
        """Runs test to detect non-randomness"""
        if len(self.runs_test_data) < 20:
            return None
        
        sequence = list(self.runs_test_data)
        runs = 1
        
        for i in range(1, len(sequence)):
            if sequence[i] != sequence[i-1]:
                runs += 1
        
        n1 = sequence.count('h')
        n2 = sequence.count('t')
        n = len(sequence)
        
        if n1 == 0 or n2 == 0:
            return None
        
        # Expected runs and variance
        expected_runs = (2 * n1 * n2) / n + 1
        variance = (2 * n1 * n2 * (2 * n1 * n2 - n)) / (n * n * (n - 1))
        
        if variance <= 0:
            return None
        
        # Z-score
        z_score = (runs - expected_runs) / math.sqrt(variance)
        
        # If too few runs (z < -1.96), sequence is not random
        if z_score < -1.5:  # Less strict threshold
            # Predict continuation of current run
            last_result = sequence[-1]
            
            return {
                'pattern_type': 'Non-random clustering',
                'predicted_side': last_result,  # Predict same as last
                'confidence': min(0.65, 0.5 + abs(z_score) / 10),
                'weight': 0.2
            }
        
        return None
    
    def _combine_analyses(self, analyses: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Combine multiple analyses"""
        if not analyses:
            return None
        
        # Weight by confidence and method weight
        h_score = 0
        t_score = 0
        total_weight = 0
        pattern_types = []
        
        for analysis in analyses:
            weight = analysis['weight'] * analysis['confidence']
            total_weight += weight
            pattern_types.append(analysis['pattern_type'])
            
            if analysis['predicted_side'] == 'h':
                h_score += weight
            else:
                t_score += weight
        
        if total_weight == 0:
            return None
        
        predicted_side = 'h' if h_score > t_score else 't'
        confidence = max(h_score, t_score) / total_weight
        
        return {
            'pattern_type': f"Combined: {', '.join(pattern_types)}",
            'predicted_side': predicted_side,
            'confidence': confidence
        }
    
    def _calculate_pattern_bet(self, analysis: Dict[str, Any]) -> int:
        """Calculate bet size based on pattern strength"""
        base_amount = self.base_bet
        confidence = analysis['confidence']
        
        # Scale bet with confidence
        multiplier = 1.0 + (confidence - 0.5) * 2  # 1.0 to 2.0 range
        
        # Apply conservative scaling
        bet_amount = int(base_amount * multiplier * 0.8)  # 80% of calculated
        
        # Ensure within limits
        max_bet = min(300, self.current_bankroll * 0.03)  # Max 3% of bankroll
        bet_amount = min(bet_amount, max_bet)
        bet_amount = max(bet_amount, 25)  # Minimum bet
        
        return bet_amount
    
    def _conservative_bet(self) -> BetInfo:
        """Place conservative bet when no clear pattern"""
        bet_amount = min(self.base_bet, self.current_bankroll * 0.01)  # 1% of bankroll
        side = random.choice(['h', 't'])
        
        return BetInfo(
            amount=max(25, int(bet_amount)),
            side=side,
            timestamp=time.time()
        )
    
    def update_result(self, bet_info: BetInfo, won: bool, profit: int):
        """Update owo-specific tracking"""
        self._update_base_stats(won, profit)
        bet_info.result = BetResult.WIN if won else BetResult.LOSS
        bet_info.profit = profit
        self.bet_history.append(bet_info)
        
        # Determine actual result
        if won:
            actual_result = bet_info.side
        else:
            actual_result = 't' if bet_info.side == 'h' else 'h'
        
        # Store detailed owo result
        owo_result = {
            'result': actual_result,
            'timestamp': bet_info.timestamp,
            'bet_side': bet_info.side,
            'won': won,
            'amount': bet_info.amount
        }
        
        self.owo_results.append(owo_result)
        self.runs_test_data.append(actual_result)
        
        # Update time patterns
        current_time = bet_info.timestamp
        current_minute = int(current_time / 60) % 60
        current_second = int(current_time) % 60
        time_key = f"{current_minute // 10}_{current_second // 10}"
        
        if time_key not in self.time_patterns:
            self.time_patterns[time_key] = Counter()
        
        self.time_patterns[time_key][actual_result] += 1
        
        # Update confidence based on recent performance
        if len(self.bet_history) >= 10:
            recent_wins = sum(1 for bet in self.bet_history[-10:] if bet.result == BetResult.WIN)
            recent_win_rate = recent_wins / 10
            
            if recent_win_rate > 0.6:
                self.confidence_multiplier = min(1.5, self.confidence_multiplier + 0.05)
            elif recent_win_rate < 0.4:
                self.confidence_multiplier = max(0.5, self.confidence_multiplier - 0.05)
        
        print(f"📊 Owo analysis updated: {len(self.owo_results)} results tracked")
    
    def should_stop_betting(self) -> bool:
        """Owo-specific stop conditions"""
        if super().should_stop_betting():
            return True
        
        # Stop if pattern analysis shows poor performance
        if len(self.bet_history) >= 15:
            recent_performance = sum(1 for bet in self.bet_history[-15:] 
                                   if bet.result == BetResult.WIN) / 15
            
            if recent_performance < 0.35:
                print(f"🛑 Stopping: Poor pattern analysis performance ({recent_performance:.1%})")
                return True
        
        return False
