#!/usr/bin/env python3
"""
Quick Restart Script for Unlimited AI Betting
Automatically restarts the system with all previous learning data
"""

import subprocess
import sys
import time
from colorama import init, Fore, Style

init()

def restart_unlimited_betting():
    """Restart the unlimited betting system with preserved learning data"""
    
    print(f"{Fore.CYAN}🔄 RESTARTING UNLIMITED AI BETTING SYSTEM{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ All previous learning data will be automatically loaded{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🧠 AI will continue from where it left off{Style.RESET_ALL}")
    
    while True:
        try:
            print(f"\n{Fore.GREEN}🚀 Starting unlimited AI betting...{Style.RESET_ALL}")
            
            # Run the unlimited betting system
            result = subprocess.run([sys.executable, "run_advanced_unlimited.py"], 
                                  check=False, capture_output=False)
            
            print(f"\n{Fore.YELLOW}🛑 System stopped with exit code: {result.returncode}{Style.RESET_ALL}")
            
            if result.returncode == 0:
                print(f"{Fore.GREEN}✅ Normal exit - system completed successfully{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}⚠️ System stopped due to safety conditions or error{Style.RESET_ALL}")
            
            # Ask if user wants to restart
            print(f"\n{Fore.CYAN}Options:{Style.RESET_ALL}")
            print(f"1. {Fore.GREEN}🔄 Restart immediately{Style.RESET_ALL}")
            print(f"2. {Fore.YELLOW}⏳ Wait and restart{Style.RESET_ALL}")
            print(f"3. {Fore.RED}❌ Exit{Style.RESET_ALL}")
            
            choice = input(f"\n{Fore.GREEN}Enter choice (1-3): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                print(f"{Fore.GREEN}🔄 Restarting immediately...{Style.RESET_ALL}")
                time.sleep(2)
                continue
                
            elif choice == "2":
                wait_time = input(f"Enter wait time in minutes (default: 5): ").strip()
                wait_time = int(wait_time) if wait_time.isdigit() else 5
                
                print(f"{Fore.YELLOW}⏳ Waiting {wait_time} minutes before restart...{Style.RESET_ALL}")
                for i in range(wait_time * 60, 0, -10):
                    mins, secs = divmod(i, 60)
                    print(f"\r{Fore.YELLOW}⏳ Restarting in {mins:02d}:{secs:02d}...{Style.RESET_ALL}", end="")
                    time.sleep(10)
                
                print(f"\n{Fore.GREEN}🔄 Restarting now...{Style.RESET_ALL}")
                continue
                
            elif choice == "3":
                print(f"{Fore.GREEN}👋 Goodbye!{Style.RESET_ALL}")
                break
                
            else:
                print(f"{Fore.RED}❌ Invalid choice{Style.RESET_ALL}")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Manual stop requested{Style.RESET_ALL}")
            
            restart_choice = input(f"{Fore.YELLOW}Restart? (y/N): {Style.RESET_ALL}").strip().lower()
            if restart_choice in ['y', 'yes']:
                continue
            else:
                break
                
        except Exception as e:
            print(f"\n{Fore.RED}❌ Error occurred: {e}{Style.RESET_ALL}")
            
            restart_choice = input(f"{Fore.YELLOW}Try to restart? (y/N): {Style.RESET_ALL}").strip().lower()
            if restart_choice in ['y', 'yes']:
                time.sleep(5)
                continue
            else:
                break

if __name__ == "__main__":
    restart_unlimited_betting()
