#!/usr/bin/env python3
"""
Quick Launcher for Discord Gambling Bot
Choose your preferred mode of operation
"""

import sys
import subprocess
from colorama import init, Fore, Style

init()

def main():
    print(f"{Fore.CYAN}🎰 DISCORD GAMBLING BOT LAUNCHER{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Choose your preferred mode:{Style.RESET_ALL}\n")
    
    print(f"1. {Fore.YELLOW}🚀 UNLIMITED AI BETTING{Style.RESET_ALL} - Advanced AI with unlimited bets")
    print(f"   • Advanced Mathematical Strategy + Owo Pattern Analyzer")
    print(f"   • AI learning with persistent data storage")
    print(f"   • Automatic strategy switching based on performance")
    print(f"   • High bankroll (100,000) for unlimited betting")
    print(f"   • All safety features enabled")
    
    print(f"\n2. {Fore.CYAN}🎯 INTERACTIVE MODE{Style.RESET_ALL} - Manual control with all options")
    print(f"   • Choose your own strategy and settings")
    print(f"   • Full control over betting parameters")
    print(f"   • Test individual features")
    print(f"   • Original main.py interface")
    
    print(f"\n3. {Fore.RED}❌ EXIT{Style.RESET_ALL}")
    
    while True:
        choice = input(f"\n{Fore.GREEN}Enter your choice (1-3): {Style.RESET_ALL}").strip()
        
        if choice == "1":
            print(f"\n{Fore.YELLOW}🚀 Launching Unlimited AI Betting System...{Style.RESET_ALL}")
            print(f"{Fore.CYAN}This will start automated betting with advanced AI learning{Style.RESET_ALL}")
            print(f"{Fore.RED}⚠️ Make sure your Discord token and channel ID are correct!{Style.RESET_ALL}")
            
            confirm = input(f"\n{Fore.YELLOW}Are you ready to start? (y/N): {Style.RESET_ALL}").strip().lower()
            if confirm in ['y', 'yes']:
                try:
                    subprocess.run([sys.executable, "run_advanced_unlimited.py"], check=True)
                except KeyboardInterrupt:
                    print(f"\n{Fore.YELLOW}🛑 Stopped by user{Style.RESET_ALL}")
                except Exception as e:
                    print(f"\n{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}Cancelled.{Style.RESET_ALL}")
                continue
            break
            
        elif choice == "2":
            print(f"\n{Fore.CYAN}🎯 Launching Interactive Mode...{Style.RESET_ALL}")
            try:
                subprocess.run([sys.executable, "main.py"], check=True)
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}🛑 Stopped by user{Style.RESET_ALL}")
            except Exception as e:
                print(f"\n{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
            break
            
        elif choice == "3":
            print(f"{Fore.GREEN}👋 Goodbye!{Style.RESET_ALL}")
            sys.exit(0)
            
        else:
            print(f"{Fore.RED}❌ Invalid choice. Please enter 1, 2, or 3.{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
