#!/usr/bin/env python3
"""
Advanced Unlimited Betting System with AI Learning
Automatically runs the most sophisticated betting strategies with unlimited bets
"""

import time
import sys
import random
from typing import Optional
from colorama import init, Fore, Style

from discord_client import DiscordClient
from bet_tracker import BetTracker
from betting_strategies.advanced_mathematical import AdvancedMathematicalStrategy
from betting_strategies.owo_pattern_analyzer import OwoPatternAnalyzer
from owo_response_parser import OwoResponseParser
from config import Config

# Initialize colorama for colored output
init()

class AdvancedUnlimitedBettingBot:
    def __init__(self):
        self.discord_client = DiscordClient()
        self.bet_tracker = BetTracker()
        self.response_parser = OwoResponseParser()
        self.config = Config()
        self.running = False
        
        # Initialize both advanced strategies
        self.mathematical_strategy = None
        self.owo_analyzer = None
        self.active_strategy = None
        
    def setup_advanced_strategies(self, bankroll: int = 50000):
        """Setup both advanced strategies with high bankroll for unlimited betting"""
        print(f"{Fore.CYAN}🚀 Setting up Advanced AI Strategies with {bankroll:,} bankroll...{Style.RESET_ALL}")

        # Setup Advanced Mathematical Strategy with more aggressive initial settings
        self.mathematical_strategy = AdvancedMathematicalStrategy(
            initial_bankroll=bankroll,
            max_bet_percentage=0.05  # Allow up to 5% of bankroll per bet
        )

        # Make it more aggressive initially to build learning data
        if hasattr(self.mathematical_strategy, 'confidence_threshold'):
            self.mathematical_strategy.confidence_threshold = 0.45  # Lower threshold initially
        if hasattr(self.mathematical_strategy, 'forced_break_probability'):
            self.mathematical_strategy.forced_break_probability = 0.1  # Less breaks initially

        # Setup Owo Pattern Analyzer
        self.owo_analyzer = OwoPatternAnalyzer(initial_bankroll=bankroll)

        # Start with Mathematical Strategy (generally performs better)
        self.active_strategy = self.mathematical_strategy
        
        print(f"{Fore.GREEN}✅ Advanced Mathematical Strategy initialized{Style.RESET_ALL}")
        print(f"{Fore.GREEN}✅ Owo Pattern Analyzer initialized{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🎯 Active Strategy: Advanced Mathematical{Style.RESET_ALL}")
        
        self.print_strategy_capabilities()
    
    def print_strategy_capabilities(self):
        """Print the AI capabilities of current strategies"""
        print(f"\n{Fore.CYAN}🧠 AI LEARNING CAPABILITIES:{Style.RESET_ALL}")
        print(f"  📊 Pattern Recognition: Advanced mathematical patterns")
        print(f"  🔗 Markov Chain Analysis: Predicts next outcomes based on sequences")
        print(f"  💰 Kelly Criterion: Optimal bet sizing for maximum growth")
        print(f"  📈 Monte Carlo Simulation: Risk assessment and probability modeling")
        print(f"  🎯 Owo-Specific Analysis: Time patterns and statistical bias detection")
        print(f"  💾 Persistent Learning: All data saved and improves over time")
        print(f"  🔄 Anti-Detection: Randomized patterns to avoid bot detection")
        print(f"  ⚡ Real-time Adaptation: Learns from every bet result")
        
    def switch_strategy(self):
        """Switch between strategies based on performance"""
        if self.active_strategy == self.mathematical_strategy:
            self.active_strategy = self.owo_analyzer
            strategy_name = "Owo Pattern Analyzer"
        else:
            self.active_strategy = self.mathematical_strategy
            strategy_name = "Advanced Mathematical"
            
        print(f"{Fore.YELLOW}🔄 Switched to: {strategy_name}{Style.RESET_ALL}")
    
    def print_comprehensive_stats(self):
        """Print comprehensive statistics for both strategies"""
        print(f"\n{Fore.CYAN}📊 COMPREHENSIVE AI STATISTICS:{Style.RESET_ALL}")
        
        # Mathematical Strategy Stats
        if self.mathematical_strategy:
            math_stats = self.mathematical_strategy.get_stats()
            print(f"\n{Fore.GREEN}🧠 Advanced Mathematical Strategy:{Style.RESET_ALL}")
            print(f"  Bankroll: {math_stats['current_bankroll']:,} (Profit: {math_stats['profit']:+,})")
            print(f"  Bets: {math_stats['total_bets']} | Win Rate: {math_stats['win_rate']:.2%}")
            print(f"  ROI: {math_stats['roi']:.2%} | Streak: W{math_stats['consecutive_wins']} L{math_stats['consecutive_losses']}")
            
            if hasattr(self.mathematical_strategy, 'result_history'):
                print(f"  🧠 Learning: {len(self.mathematical_strategy.result_history)} results tracked")
                print(f"  🔗 Patterns: {len(self.mathematical_strategy.pattern_memory)} learned")
                print(f"  📊 Markov States: {len(self.mathematical_strategy.transition_matrix)}")
        
        # Owo Analyzer Stats  
        if self.owo_analyzer:
            owo_stats = self.owo_analyzer.get_stats()
            print(f"\n{Fore.YELLOW}🎯 Owo Pattern Analyzer:{Style.RESET_ALL}")
            print(f"  Bankroll: {owo_stats['current_bankroll']:,} (Profit: {owo_stats['profit']:+,})")
            print(f"  Bets: {owo_stats['total_bets']} | Win Rate: {owo_stats['win_rate']:.2%}")
            print(f"  ROI: {owo_stats['roi']:.2%} | Streak: W{owo_stats['consecutive_wins']} L{owo_stats['consecutive_losses']}")
            
            if hasattr(self.owo_analyzer, 'owo_results'):
                print(f"  🎯 Owo Results: {len(self.owo_analyzer.owo_results)} analyzed")
                print(f"  ⏰ Time Patterns: {len(self.owo_analyzer.time_patterns)} discovered")
    
    def check_bet_results(self):
        """Enhanced result checking with dual strategy updates"""
        pending_bets = self.bet_tracker.get_pending_bets()
        if not pending_bets:
            return

        print(f"🔍 Checking results for {len(pending_bets)} pending bets...")
        messages = self.discord_client.get_recent_messages(limit=20)
        if not messages:
            print("⚠️ Could not fetch messages")
            return

        for bet_info in pending_bets.copy():
            owo_response = self.discord_client.find_owo_response(messages, bet_info.timestamp, self.config.USERNAME_IN_SERVER)

            if owo_response:
                result = self.response_parser.parse_coinflip_result(
                    owo_response.get('content', ''),
                    bet_info.amount
                )

                if result:
                    # Store actual result for advanced analysis
                    if hasattr(result, 'actual_result') and result.actual_result:
                        bet_info.actual_result = result.actual_result
                    elif hasattr(result, 'side_result') and result.side_result:
                        bet_info.actual_result = result.side_result

                    # Update bet tracker
                    self.bet_tracker.complete_bet(bet_info, result.won, result.profit)

                    # Update both strategies for learning
                    if self.mathematical_strategy:
                        self.mathematical_strategy.update_result(bet_info, result.won, result.profit)
                    if self.owo_analyzer:
                        self.owo_analyzer.update_result(bet_info, result.won, result.profit)

                    print(f"📊 Result: {'WIN' if result.won else 'LOSS'} - Profit: {result.profit:+d}")
                    print(f"🧠 Both AI strategies updated with new learning data")
    
    def place_intelligent_bet(self) -> bool:
        """Place bet using active strategy with intelligence switching"""
        if not self.active_strategy:
            print(f"{Fore.RED}❌ No strategy active{Style.RESET_ALL}")
            return False

        # Check if we should switch strategies based on performance
        if (self.active_strategy.total_bets > 0 and
            self.active_strategy.total_bets % 20 == 0 and
            self.active_strategy.win_rate < 0.45):
            print(f"{Fore.YELLOW}🔄 Performance below 45%, considering strategy switch...{Style.RESET_ALL}")
            self.switch_strategy()

        # Check stop conditions
        if self.active_strategy.should_stop_betting():
            print(f"{Fore.RED}🛑 Active strategy says to stop betting{Style.RESET_ALL}")
            return False

        # Get next bet from active strategy
        bet_info = self.active_strategy.calculate_next_bet()

        # Bootstrap mode: If no prediction available and we have few bets, place random bets to build data
        if not bet_info and self.active_strategy.total_bets < 10:
            print(f"{Fore.CYAN}🎲 Bootstrap mode: Placing random bet to build AI learning data{Style.RESET_ALL}")
            from betting_strategies.base_strategy import BetInfo
            bet_info = BetInfo(
                amount=min(500, int(self.active_strategy.current_bankroll * 0.005)),  # 0.5% of bankroll
                side=random.choice(['h', 't']),
                timestamp=time.time()
            )

        if not bet_info:
            print(f"{Fore.YELLOW}⚠️ Strategy returned no bet, waiting...{Style.RESET_ALL}")
            return False

        # Place the bet
        success = self.discord_client.place_bet(bet_info.amount, bet_info.side)
        if success:
            self.bet_tracker.add_pending_bet(bet_info)
            print(f"{Fore.GREEN}✅ Intelligent bet placed: {bet_info.amount} ({bet_info.side or 'random'}){Style.RESET_ALL}")

            # Wait for owo response
            response_wait = random.uniform(4, 7)
            print(f"⏳ Waiting {response_wait:.1f}s for owo bot response...")
            time.sleep(response_wait)
            self.check_bet_results()

            return True
        else:
            print(f"{Fore.RED}❌ Failed to place bet{Style.RESET_ALL}")
            return False
    
    def run_unlimited_ai_betting(self):
        """Run unlimited betting with advanced AI learning"""
        if not self.active_strategy:
            print(f"{Fore.RED}❌ No strategy configured{Style.RESET_ALL}")
            return

        self.running = True
        bet_count = 0
        session_start_bankroll = self.active_strategy.current_bankroll

        print(f"\n{Fore.GREEN}🚀 STARTING UNLIMITED AI BETTING SYSTEM{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💰 Starting bankroll: {session_start_bankroll:,}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🧠 AI Learning: ENABLED - All patterns will be learned and saved{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 Strategy Switching: ENABLED - Will switch if performance drops{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚠️ Safety: Will stop after 10 consecutive losses{Style.RESET_ALL}")
        print(f"{Fore.RED}🛑 Press Ctrl+C to stop manually{Style.RESET_ALL}")

        try:
            while self.running:
                # Safety check for consecutive losses
                if self.active_strategy.consecutive_losses >= 10:
                    print(f"{Fore.RED}🛑 SAFETY STOP: 10 consecutive losses reached!{Style.RESET_ALL}")
                    break

                success = self.place_intelligent_bet()
                if success:
                    bet_count += 1

                    # Print comprehensive stats every 5 bets
                    if bet_count % 5 == 0:
                        self.print_comprehensive_stats()
                        session_profit = self.active_strategy.current_bankroll - session_start_bankroll
                        print(f"{Fore.CYAN}📈 Session: {bet_count} bets, {session_profit:+,} profit{Style.RESET_ALL}")

                # Intelligent wait time with performance-based adjustments
                base_wait = random.uniform(self.config.MIN_BET_INTERVAL, self.config.MAX_BET_INTERVAL)
                
                # Add extra wait after losses to avoid tilt
                if self.active_strategy.consecutive_losses >= 5:
                    base_wait += random.uniform(3, 6)
                elif self.active_strategy.consecutive_losses >= 8:
                    base_wait += random.uniform(8, 12)

                print(f"⏳ Intelligent wait: {base_wait:.1f}s (AI-optimized timing)...")
                time.sleep(base_wait)

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Manual stop requested...{Style.RESET_ALL}")
        finally:
            self.running = False
            self.save_all_learning_data()
            self.print_final_session_summary(bet_count, session_start_bankroll)
    
    def save_all_learning_data(self):
        """Save learning data for both strategies"""
        print(f"{Fore.CYAN}💾 Saving all AI learning data...{Style.RESET_ALL}")
        
        if self.mathematical_strategy and hasattr(self.mathematical_strategy, 'save_learning_data'):
            self.mathematical_strategy.save_learning_data()
            
        if self.owo_analyzer and hasattr(self.owo_analyzer, 'save_learning_data'):
            self.owo_analyzer.save_learning_data()
            
        print(f"{Fore.GREEN}✅ All learning data saved - will persist for next session!{Style.RESET_ALL}")
    
    def print_final_session_summary(self, bet_count: int, session_start_bankroll: int):
        """Print comprehensive final summary"""
        session_profit = self.active_strategy.current_bankroll - session_start_bankroll
        session_roi = session_profit / session_start_bankroll if session_start_bankroll > 0 else 0

        print(f"\n{Fore.CYAN}🏁 UNLIMITED AI BETTING SESSION COMPLETE{Style.RESET_ALL}")
        print(f"{'='*60}")
        print(f"  Total Bets Placed: {bet_count}")
        print(f"  Starting Bankroll: {session_start_bankroll:,}")
        print(f"  Ending Bankroll: {self.active_strategy.current_bankroll:,}")
        print(f"  Session Profit: {session_profit:+,}")
        print(f"  Session ROI: {session_roi:+.2%}")
        print(f"  Final Streak: {self.active_strategy.consecutive_wins} wins, {self.active_strategy.consecutive_losses} losses")
        print(f"{'='*60}")
        
        self.print_comprehensive_stats()

def main():
    """Main function to run the advanced unlimited betting system"""
    bot = AdvancedUnlimitedBettingBot()
    
    print(f"{Fore.CYAN}🎰 ADVANCED UNLIMITED AI BETTING SYSTEM{Style.RESET_ALL}")
    print(f"{Fore.GREEN}🧠 Features: Advanced AI, Pattern Learning, Unlimited Bets{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚡ Auto-configured for maximum performance{Style.RESET_ALL}")
    
    # Auto-setup with high bankroll for unlimited betting
    bankroll = 100000  # High starting bankroll for unlimited betting
    bot.setup_advanced_strategies(bankroll)
    
    print(f"\n{Fore.GREEN}🚀 Ready to start unlimited AI betting!{Style.RESET_ALL}")
    input(f"{Fore.YELLOW}Press Enter to begin unlimited betting with AI learning...{Style.RESET_ALL}")
    
    # Start unlimited betting
    bot.run_unlimited_ai_betting()

if __name__ == "__main__":
    main()
