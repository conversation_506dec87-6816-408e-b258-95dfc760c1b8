#!/usr/bin/env python3
"""
STRATEGY SWITCH - Forces switch to a different strategy to resume betting
"""

import subprocess
import sys
from colorama import init, Fore, Style

init()

def switch_strategy():
    """Switch to a different strategy to bypass current safety stop"""
    
    print(f"{Fore.CYAN}🔄 STRATEGY SWITCH MODE{Style.RESET_ALL}")
    print(f"{Fore.GREEN}This will switch to a different AI strategy to resume betting{Style.RESET_ALL}")
    
    print(f"\n{Fore.YELLOW}Available strategies:{Style.RESET_ALL}")
    print(f"1. {Fore.CYAN}Owo Pattern Analyzer{Style.RESET_ALL} - Specialized for owo bot patterns")
    print(f"2. {Fore.MAGENTA}Arbitrage Strategy{Style.RESET_ALL} - Looks for arbitrage opportunities")
    print(f"3. {Fore.GREEN}Fresh Mathematical{Style.RESET_ALL} - Reset mathematical strategy")
    print(f"4. {Fore.RED}Force Continue Current{Style.RESET_ALL} - Override safety stops")
    
    choice = input(f"\n{Fore.GREEN}Enter choice (1-4): {Style.RESET_ALL}").strip()
    
    if choice == "1":
        print(f"{Fore.CYAN}🎯 Switching to Owo Pattern Analyzer...{Style.RESET_ALL}")
        # Modify to start with Owo strategy
        modify_and_launch("self.owo_analyzer")
        
    elif choice == "2":
        print(f"{Fore.MAGENTA}💎 Switching to Arbitrage Strategy...{Style.RESET_ALL}")
        # Modify to start with Arbitrage strategy
        modify_and_launch("self.arbitrage_strategy")
        
    elif choice == "3":
        print(f"{Fore.GREEN}🧠 Resetting Mathematical Strategy...{Style.RESET_ALL}")
        # Fresh restart
        subprocess.run([sys.executable, "MAX.py"])
        
    elif choice == "4":
        print(f"{Fore.RED}🚀 Force continuing with current strategy...{Style.RESET_ALL}")
        subprocess.run([sys.executable, "FORCE_CONTINUE.py"])
        
    else:
        print(f"{Fore.RED}❌ Invalid choice{Style.RESET_ALL}")

def modify_and_launch(strategy_name):
    """Modify the system to start with a specific strategy"""
    try:
        with open("PERFECT_MAX.py", "r") as f:
            content = f.read()
        
        # Modify to start with the chosen strategy
        modified_content = content.replace(
            "self.active_strategy = self.mathematical_strategy",
            f"self.active_strategy = {strategy_name}"
        )
        
        # Also disable the safety stop for this strategy
        modified_content = modified_content.replace(
            "if self.active_strategy.should_stop_betting():",
            "if False:  # Strategy switch - safety disabled"
        )
        
        # Save the modified version
        with open("SWITCHED_MAX.py", "w") as f:
            f.write(modified_content)
        
        print(f"{Fore.GREEN}✅ Created SWITCHED_MAX.py with new strategy{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🚀 Launching with switched strategy...{Style.RESET_ALL}")
        
        # Launch the switched version
        subprocess.run([sys.executable, "SWITCHED_MAX.py"])
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 Falling back to normal restart...{Style.RESET_ALL}")
        subprocess.run([sys.executable, "MAX.py"])

if __name__ == "__main__":
    switch_strategy()
