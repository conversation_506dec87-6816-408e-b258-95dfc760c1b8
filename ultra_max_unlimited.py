#!/usr/bin/env python3
"""
ULTRA MAXIMUM UNLIMITED AI BETTING SYSTEM
- ALL parameters set to MAXIMUM
- UNLIMITED betting with highest bankroll
- ALL learning capabilities MAXED
- ARBITRAGE opportunities MAXIMIZED
- CRASH protection and auto-recovery
- HIGHEST performance settings
"""

import time
import sys
import random
import traceback
import json
from typing import Optional
from colorama import init, Fore, Style

from discord_client import DiscordClient
from bet_tracker import BetTracker
from betting_strategies.advanced_mathematical import AdvancedMathematicalStrategy
from betting_strategies.owo_pattern_analyzer import OwoPatternAnalyzer
from betting_strategies.arbitrage import ArbitrageStrategy
from betting_strategies.dynamic_adaptive import DynamicAdaptiveStrategy
from owo_response_parser import OwoResponseParser
from config import Config

# Initialize colorama for colored output
init()

class UltraMaxUnlimitedBettingBot:
    def __init__(self):
        self.discord_client = DiscordClient()
        self.bet_tracker = BetTracker()
        self.response_parser = OwoResponseParser()
        self.config = Config()
        self.running = False
        
        # Initialize ALL advanced strategies with MAXIMUM settings
        self.mathematical_strategy = None
        self.owo_analyzer = None
        self.arbitrage_strategy = None
        self.dynamic_strategy = None
        self.active_strategy = None
        self.strategy_rotation = []
        self.current_strategy_index = 0
        
        # Ultra performance tracking
        self.session_stats = {
            'total_bets': 0,
            'total_profit': 0,
            'strategy_switches': 0,
            'crashes_recovered': 0,
            'arbitrage_opportunities': 0
        }
        
    def setup_ultra_max_strategies(self, bankroll: int = 1000000):
        """Setup ALL strategies with MAXIMUM settings for unlimited betting"""
        print(f"{Fore.CYAN}🚀 SETTING UP ULTRA MAXIMUM AI STRATEGIES{Style.RESET_ALL}")
        print(f"{Fore.GREEN}💰 Ultra High Bankroll: {bankroll:,}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚡ ALL parameters set to MAXIMUM{Style.RESET_ALL}")
        
        try:
            # 1. ADVANCED MATHEMATICAL STRATEGY - MAXED OUT
            self.mathematical_strategy = AdvancedMathematicalStrategy(
                initial_bankroll=bankroll,
                max_bet_percentage=0.10  # 10% of bankroll per bet (MAXIMUM)
            )
            
            # MAX OUT mathematical strategy parameters
            if hasattr(self.mathematical_strategy, 'confidence_threshold'):
                self.mathematical_strategy.confidence_threshold = 0.35  # Lower = more aggressive
            if hasattr(self.mathematical_strategy, 'forced_break_probability'):
                self.mathematical_strategy.forced_break_probability = 0.05  # Minimal breaks
            if hasattr(self.mathematical_strategy, 'max_consecutive_bets'):
                self.mathematical_strategy.max_consecutive_bets = 100  # Maximum consecutive bets
            if hasattr(self.mathematical_strategy, 'edge_threshold'):
                self.mathematical_strategy.edge_threshold = 0.01  # 1% minimum edge (aggressive)
            if hasattr(self.mathematical_strategy, 'max_pattern_length'):
                self.mathematical_strategy.max_pattern_length = 15  # Maximum pattern analysis
            if hasattr(self.mathematical_strategy, 'result_history'):
                # Create new deque with larger maxlen (can't modify existing maxlen)
                from collections import deque
                old_data = list(self.mathematical_strategy.result_history)
                self.mathematical_strategy.result_history = deque(old_data, maxlen=500)
                
            print(f"{Fore.GREEN}✅ Advanced Mathematical Strategy: MAXED OUT{Style.RESET_ALL}")
            
            # 2. OWO PATTERN ANALYZER - MAXED OUT
            self.owo_analyzer = OwoPatternAnalyzer(initial_bankroll=bankroll)
            
            # MAX OUT owo analyzer parameters
            if hasattr(self.owo_analyzer, 'owo_results'):
                # Create new deque with larger maxlen
                from collections import deque
                old_owo_data = list(self.owo_analyzer.owo_results)
                self.owo_analyzer.owo_results = deque(old_owo_data, maxlen=1000)
            if hasattr(self.owo_analyzer, 'base_bet'):
                self.owo_analyzer.base_bet = 1000  # Higher base bet
            if hasattr(self.owo_analyzer, 'confidence_multiplier'):
                self.owo_analyzer.confidence_multiplier = 2.0  # Maximum confidence
            if hasattr(self.owo_analyzer, 'chi_square_threshold'):
                self.owo_analyzer.chi_square_threshold = 2.0  # More sensitive detection
            if hasattr(self.owo_analyzer, 'pattern_masking'):
                self.owo_analyzer.pattern_masking = False  # Disable masking for max aggression
                
            print(f"{Fore.GREEN}✅ Owo Pattern Analyzer: MAXED OUT{Style.RESET_ALL}")
            
            # 3. ARBITRAGE STRATEGY - MAXED OUT
            self.arbitrage_strategy = ArbitrageStrategy(
                initial_bankroll=bankroll,
                base_bet=2000,  # High base bet
                min_edge=0.005  # 0.5% minimum edge (very aggressive)
            )
            
            print(f"{Fore.GREEN}✅ Arbitrage Strategy: MAXED OUT{Style.RESET_ALL}")
            
            # 4. DYNAMIC ADAPTIVE STRATEGY - MAXED OUT
            self.dynamic_strategy = DynamicAdaptiveStrategy(
                initial_bankroll=bankroll,
                base_bet_percentage=0.08  # 8% of bankroll (very aggressive)
            )
            
            print(f"{Fore.GREEN}✅ Dynamic Adaptive Strategy: MAXED OUT{Style.RESET_ALL}")
            
            # Setup strategy rotation for maximum performance
            self.strategy_rotation = [
                self.mathematical_strategy,
                self.owo_analyzer,
                self.arbitrage_strategy,
                self.dynamic_strategy
            ]
            
            # Start with mathematical strategy
            self.active_strategy = self.mathematical_strategy
            
            print(f"{Fore.YELLOW}🎯 Active Strategy: Advanced Mathematical (will rotate for max performance){Style.RESET_ALL}")
            
            self.print_ultra_capabilities()
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error setting up strategies: {e}{Style.RESET_ALL}")
            self.handle_crash("Strategy Setup", e)
    
    def print_ultra_capabilities(self):
        """Print the ULTRA MAXIMUM AI capabilities"""
        print(f"\n{Fore.CYAN}🧠 ULTRA MAXIMUM AI CAPABILITIES:{Style.RESET_ALL}")
        print(f"  🚀 UNLIMITED BETTING: No limits, maximum bankroll")
        print(f"  🧠 ADVANCED MATHEMATICAL: 15-pattern analysis, 500 result history")
        print(f"  🎯 OWO PATTERN ANALYZER: 1000 result history, maximum sensitivity")
        print(f"  💎 ARBITRAGE DETECTION: 0.5% edge threshold, maximum opportunities")
        print(f"  ⚡ DYNAMIC ADAPTATION: 8% bankroll bets, ultra-aggressive")
        print(f"  🔄 STRATEGY ROTATION: Auto-switch for maximum performance")
        print(f"  💾 MAXIMUM LEARNING: All data saved, persistent across sessions")
        print(f"  🛡️ CRASH PROTECTION: Auto-recovery from all errors")
        print(f"  📊 REAL-TIME OPTIMIZATION: Continuous parameter adjustment")
        print(f"  🎲 BOOTSTRAP MODE: Aggressive initial learning")
        print(f"  ⏰ TIMING OPTIMIZATION: Maximum speed with owo compatibility")
        
    def intelligent_strategy_rotation(self):
        """Rotate strategies based on performance for maximum efficiency"""
        try:
            current_performance = self.active_strategy.win_rate if self.active_strategy.total_bets > 0 else 0.5
            
            # Rotate if performance drops or every 15 bets for optimization
            should_rotate = (
                (self.active_strategy.total_bets > 0 and current_performance < 0.40) or
                (self.active_strategy.total_bets > 0 and self.active_strategy.total_bets % 15 == 0) or
                (self.active_strategy.consecutive_losses >= 5)
            )
            
            if should_rotate:
                # Find best performing strategy
                best_strategy = self.active_strategy
                best_performance = current_performance
                
                for strategy in self.strategy_rotation:
                    if strategy.total_bets > 0:
                        strategy_performance = strategy.win_rate
                        if strategy_performance > best_performance:
                            best_strategy = strategy
                            best_performance = strategy_performance
                
                # If we found a better strategy, switch to it
                if best_strategy != self.active_strategy:
                    old_strategy_name = type(self.active_strategy).__name__
                    self.active_strategy = best_strategy
                    new_strategy_name = type(self.active_strategy).__name__
                    
                    self.session_stats['strategy_switches'] += 1
                    
                    print(f"{Fore.YELLOW}🔄 STRATEGY ROTATION: {old_strategy_name} → {new_strategy_name}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}📈 Performance improvement: {current_performance:.2%} → {best_performance:.2%}{Style.RESET_ALL}")
                    
        except Exception as e:
            print(f"{Fore.RED}⚠️ Strategy rotation error: {e}{Style.RESET_ALL}")
            self.handle_crash("Strategy Rotation", e)
    
    def detect_arbitrage_opportunities(self):
        """Detect and exploit arbitrage opportunities"""
        try:
            if self.arbitrage_strategy and hasattr(self.arbitrage_strategy, 'get_arbitrage_stats'):
                arb_stats = self.arbitrage_strategy.get_arbitrage_stats()
                arb_data = arb_stats.get('arbitrage_data', {})
                
                next_opp = arb_data.get('next_opportunity')
                if next_opp and next_opp.get('confidence', 0) > 0.7:
                    self.session_stats['arbitrage_opportunities'] += 1
                    print(f"{Fore.CYAN}💎 ARBITRAGE OPPORTUNITY DETECTED:{Style.RESET_ALL}")
                    print(f"   Type: {next_opp['type']}")
                    print(f"   Confidence: {next_opp['confidence']:.1%}")
                    print(f"   Reason: {next_opp['reason']}")
                    
                    # Temporarily switch to arbitrage strategy for this opportunity
                    if self.active_strategy != self.arbitrage_strategy:
                        print(f"{Fore.YELLOW}🔄 Switching to arbitrage strategy for opportunity{Style.RESET_ALL}")
                        self.active_strategy = self.arbitrage_strategy
                        
        except Exception as e:
            print(f"{Fore.RED}⚠️ Arbitrage detection error: {e}{Style.RESET_ALL}")
            self.handle_crash("Arbitrage Detection", e)
    
    def print_ultra_comprehensive_stats(self):
        """Print ultra-comprehensive statistics for all strategies"""
        try:
            print(f"\n{Fore.CYAN}📊 ULTRA COMPREHENSIVE AI STATISTICS:{Style.RESET_ALL}")
            
            # Session stats
            print(f"\n{Fore.YELLOW}🎯 SESSION PERFORMANCE:{Style.RESET_ALL}")
            print(f"  Total Bets: {self.session_stats['total_bets']}")
            print(f"  Total Profit: {self.session_stats['total_profit']:+,}")
            print(f"  Strategy Switches: {self.session_stats['strategy_switches']}")
            print(f"  Arbitrage Opportunities: {self.session_stats['arbitrage_opportunities']}")
            print(f"  Crashes Recovered: {self.session_stats['crashes_recovered']}")
            
            # All strategy stats
            strategies = [
                ("🧠 Advanced Mathematical", self.mathematical_strategy),
                ("🎯 Owo Pattern Analyzer", self.owo_analyzer),
                ("💎 Arbitrage Strategy", self.arbitrage_strategy),
                ("⚡ Dynamic Adaptive", self.dynamic_strategy)
            ]
            
            for name, strategy in strategies:
                if strategy:
                    stats = strategy.get_stats()
                    print(f"\n{Fore.GREEN}{name}:{Style.RESET_ALL}")
                    print(f"  Bankroll: {stats['current_bankroll']:,} (Profit: {stats['profit']:+,})")
                    print(f"  Bets: {stats['total_bets']} | Win Rate: {stats['win_rate']:.2%}")
                    print(f"  ROI: {stats['roi']:.2%} | Streak: W{stats['consecutive_wins']} L{stats['consecutive_losses']}")
                    
                    # Strategy-specific stats
                    if hasattr(strategy, 'result_history') and strategy.result_history:
                        print(f"  🧠 Learning: {len(strategy.result_history)} results tracked")
                    if hasattr(strategy, 'pattern_memory') and strategy.pattern_memory:
                        print(f"  🔗 Patterns: {len(strategy.pattern_memory)} learned")
                    if hasattr(strategy, 'owo_results') and strategy.owo_results:
                        print(f"  🎯 Owo Results: {len(strategy.owo_results)} analyzed")
                        
        except Exception as e:
            print(f"{Fore.RED}⚠️ Stats display error: {e}{Style.RESET_ALL}")
            self.handle_crash("Stats Display", e)
    
    def handle_crash(self, operation: str, error: Exception):
        """Handle crashes with auto-recovery"""
        try:
            self.session_stats['crashes_recovered'] += 1
            
            print(f"{Fore.RED}💥 CRASH DETECTED in {operation}: {error}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}🛡️ AUTO-RECOVERY INITIATED (Recovery #{self.session_stats['crashes_recovered']}){Style.RESET_ALL}")
            
            # Log the crash
            crash_log = {
                'timestamp': time.time(),
                'operation': operation,
                'error': str(error),
                'traceback': traceback.format_exc(),
                'recovery_count': self.session_stats['crashes_recovered']
            }
            
            try:
                with open('crash_log.json', 'a') as f:
                    f.write(json.dumps(crash_log) + '\n')
            except:
                pass  # Don't crash on logging
            
            # Auto-recovery actions
            if "strategy" in operation.lower():
                # Reset to mathematical strategy if strategy error
                if self.mathematical_strategy:
                    self.active_strategy = self.mathematical_strategy
                    print(f"{Fore.GREEN}✅ Reset to Mathematical Strategy{Style.RESET_ALL}")
            
            # Wait a bit before continuing
            time.sleep(2)
            
            print(f"{Fore.GREEN}✅ AUTO-RECOVERY COMPLETE - Continuing operation{Style.RESET_ALL}")
            
        except Exception as recovery_error:
            print(f"{Fore.RED}💥 RECOVERY FAILED: {recovery_error}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}⚠️ Manual intervention may be required{Style.RESET_ALL}")
    
    def check_bet_results_ultra(self):
        """Ultra-enhanced result checking with crash protection"""
        try:
            pending_bets = self.bet_tracker.get_pending_bets()
            if not pending_bets:
                return

            print(f"🔍 Checking results for {len(pending_bets)} pending bets...")
            messages = self.discord_client.get_recent_messages(limit=25)
            if not messages:
                print("⚠️ Could not fetch messages")
                return

            for bet_info in pending_bets.copy():
                try:
                    owo_response = self.discord_client.find_owo_response(
                        messages, bet_info.timestamp, self.config.USERNAME_IN_SERVER
                    )

                    if owo_response:
                        result = self.response_parser.parse_coinflip_result(
                            owo_response.get('content', ''),
                            bet_info.amount
                        )

                        if result:
                            # Store actual result for advanced analysis
                            if hasattr(result, 'actual_result') and result.actual_result:
                                bet_info.actual_result = result.actual_result
                            elif hasattr(result, 'side_result') and result.side_result:
                                bet_info.actual_result = result.side_result

                            # Update bet tracker
                            self.bet_tracker.complete_bet(bet_info, result.won, result.profit)

                            # Update ALL strategies for maximum learning
                            for strategy in self.strategy_rotation:
                                if strategy:
                                    try:
                                        strategy.update_result(bet_info, result.won, result.profit)
                                    except Exception as e:
                                        print(f"⚠️ Strategy update error: {e}")

                            # Update session stats
                            self.session_stats['total_bets'] += 1
                            self.session_stats['total_profit'] += result.profit

                            print(f"📊 Result: {'WIN' if result.won else 'LOSS'} - Profit: {result.profit:+d}")
                            print(f"🧠 ALL AI strategies updated with new learning data")
                            
                except Exception as e:
                    print(f"⚠️ Error processing bet result: {e}")
                    self.handle_crash("Bet Result Processing", e)
                    
        except Exception as e:
            print(f"⚠️ Error in result checking: {e}")
            self.handle_crash("Result Checking", e)
    
    def place_ultra_intelligent_bet(self) -> bool:
        """Place bet using ultra-intelligent strategy selection with crash protection"""
        try:
            if not self.active_strategy:
                print(f"{Fore.RED}❌ No strategy active{Style.RESET_ALL}")
                return False

            # Detect arbitrage opportunities
            self.detect_arbitrage_opportunities()
            
            # Intelligent strategy rotation
            self.intelligent_strategy_rotation()

            # Check stop conditions
            if self.active_strategy.should_stop_betting():
                print(f"{Fore.RED}🛑 Active strategy says to stop betting{Style.RESET_ALL}")
                return False

            # Get next bet from active strategy
            bet_info = self.active_strategy.calculate_next_bet()
            
            # Ultra-aggressive bootstrap mode for maximum learning
            if not bet_info and self.active_strategy.total_bets < 20:
                print(f"{Fore.CYAN}🎲 ULTRA BOOTSTRAP: Placing aggressive bet for maximum AI learning{Style.RESET_ALL}")
                from betting_strategies.base_strategy import BetInfo
                bet_info = BetInfo(
                    amount=min(2000, int(self.active_strategy.current_bankroll * 0.01)),  # 1% of bankroll
                    side=random.choice(['h', 't']),
                    timestamp=time.time()
                )
            
            if not bet_info:
                print(f"{Fore.YELLOW}⚠️ Strategy returned no bet, optimizing...{Style.RESET_ALL}")
                return False

            # Place the bet with crash protection
            try:
                success = self.discord_client.place_bet(bet_info.amount, bet_info.side)
                if success:
                    self.bet_tracker.add_pending_bet(bet_info)
                    print(f"{Fore.GREEN}✅ ULTRA bet placed: {bet_info.amount} ({bet_info.side or 'random'}) via {type(self.active_strategy).__name__}{Style.RESET_ALL}")

                    # Wait for owo response with optimized timing
                    response_wait = random.uniform(3, 6)  # Faster response checking
                    print(f"⏳ Waiting {response_wait:.1f}s for owo bot response...")
                    time.sleep(response_wait)
                    self.check_bet_results_ultra()

                    return True
                else:
                    print(f"{Fore.RED}❌ Failed to place bet{Style.RESET_ALL}")
                    return False
                    
            except Exception as e:
                print(f"{Fore.RED}❌ Bet placement error: {e}{Style.RESET_ALL}")
                self.handle_crash("Bet Placement", e)
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Ultra betting error: {e}{Style.RESET_ALL}")
            self.handle_crash("Ultra Betting", e)
            return False

    def run_ultra_max_unlimited_betting(self):
        """Run ULTRA MAXIMUM unlimited betting with ALL features maxed"""
        if not self.active_strategy:
            print(f"{Fore.RED}❌ No strategy configured{Style.RESET_ALL}")
            return

        self.running = True
        bet_count = 0
        session_start_bankroll = self.active_strategy.current_bankroll
        last_save_time = time.time()

        print(f"\n{Fore.RED}🚀 STARTING ULTRA MAXIMUM UNLIMITED AI BETTING SYSTEM{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💰 Ultra High Bankroll: {session_start_bankroll:,}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}🧠 ALL AI Learning: MAXED OUT{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💎 Arbitrage Detection: MAXIMUM SENSITIVITY{Style.RESET_ALL}")
        print(f"{Fore.MAGENTA}🔄 Strategy Rotation: INTELLIGENT AUTO-SWITCHING{Style.RESET_ALL}")
        print(f"{Fore.BLUE}🛡️ Crash Protection: FULL AUTO-RECOVERY{Style.RESET_ALL}")
        print(f"{Fore.WHITE}⚡ Performance: ALL PARAMETERS MAXED{Style.RESET_ALL}")
        print(f"{Fore.RED}🛑 Press Ctrl+C to stop manually{Style.RESET_ALL}")

        try:
            while self.running:
                try:
                    # Ultra-aggressive safety check (higher threshold for unlimited play)
                    if self.active_strategy.consecutive_losses >= 15:  # Increased from 10
                        print(f"{Fore.RED}🛑 ULTRA SAFETY STOP: 15 consecutive losses reached!{Style.RESET_ALL}")
                        break

                    success = self.place_ultra_intelligent_bet()
                    if success:
                        bet_count += 1

                        # Print comprehensive stats every 3 bets
                        if bet_count % 3 == 0:
                            self.print_ultra_comprehensive_stats()
                            session_profit = self.session_stats['total_profit']
                            print(f"{Fore.CYAN}📈 ULTRA SESSION: {bet_count} bets, {session_profit:+,} profit{Style.RESET_ALL}")

                    # Ultra-optimized wait time (faster than normal for max performance)
                    base_wait = random.uniform(8, 12)  # Slightly faster than config

                    # Performance-based wait adjustments
                    if self.active_strategy.consecutive_losses >= 8:
                        base_wait += random.uniform(2, 4)  # Brief pause after losses
                    elif self.active_strategy.consecutive_wins >= 5:
                        base_wait = max(6, base_wait - 2)  # Faster when winning

                    print(f"⏳ Ultra-optimized wait: {base_wait:.1f}s (maximum performance timing)...")
                    time.sleep(base_wait)

                    # Auto-save learning data every 5 minutes
                    if time.time() - last_save_time > 300:  # 5 minutes
                        self.save_all_ultra_learning_data()
                        last_save_time = time.time()

                except Exception as e:
                    print(f"{Fore.RED}💥 Main loop error: {e}{Style.RESET_ALL}")
                    self.handle_crash("Main Loop", e)
                    time.sleep(5)  # Brief pause before continuing

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}🛑 Manual stop requested...{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}💥 Critical error: {e}{Style.RESET_ALL}")
            self.handle_crash("Critical System", e)
        finally:
            self.running = False
            self.save_all_ultra_learning_data()
            self.print_ultra_final_summary(bet_count, session_start_bankroll)

    def save_all_ultra_learning_data(self):
        """Save learning data for ALL strategies with crash protection"""
        try:
            print(f"{Fore.CYAN}💾 Saving ALL ultra learning data...{Style.RESET_ALL}")

            saved_count = 0
            for strategy in self.strategy_rotation:
                if strategy and hasattr(strategy, 'save_learning_data'):
                    try:
                        strategy.save_learning_data()
                        saved_count += 1
                    except Exception as e:
                        print(f"⚠️ Error saving {type(strategy).__name__}: {e}")

            print(f"{Fore.GREEN}✅ {saved_count}/4 strategies saved - Ultra learning data preserved!{Style.RESET_ALL}")

        except Exception as e:
            print(f"{Fore.RED}⚠️ Ultra save error: {e}{Style.RESET_ALL}")
            self.handle_crash("Ultra Save", e)

    def print_ultra_final_summary(self, bet_count: int, session_start_bankroll: int):
        """Print ultra-comprehensive final summary"""
        try:
            session_profit = self.session_stats['total_profit']
            session_roi = session_profit / session_start_bankroll if session_start_bankroll > 0 else 0

            print(f"\n{Fore.RED}🏁 ULTRA MAXIMUM UNLIMITED BETTING SESSION COMPLETE{Style.RESET_ALL}")
            print(f"{'='*80}")
            print(f"  🎯 Total Bets Placed: {bet_count}")
            print(f"  💰 Starting Bankroll: {session_start_bankroll:,}")
            print(f"  💰 Ending Bankroll: {self.active_strategy.current_bankroll:,}")
            print(f"  📈 Session Profit: {session_profit:+,}")
            print(f"  📊 Session ROI: {session_roi:+.2%}")
            print(f"  🔄 Strategy Switches: {self.session_stats['strategy_switches']}")
            print(f"  💎 Arbitrage Opportunities: {self.session_stats['arbitrage_opportunities']}")
            print(f"  🛡️ Crashes Recovered: {self.session_stats['crashes_recovered']}")
            print(f"  ⚡ Final Streak: {self.active_strategy.consecutive_wins} wins, {self.active_strategy.consecutive_losses} losses")
            print(f"{'='*80}")

            self.print_ultra_comprehensive_stats()

        except Exception as e:
            print(f"{Fore.RED}⚠️ Summary error: {e}{Style.RESET_ALL}")
            self.handle_crash("Final Summary", e)

def main():
    """Main function to run the ULTRA MAXIMUM unlimited betting system"""
    try:
        bot = UltraMaxUnlimitedBettingBot()

        print(f"{Fore.RED}🎰 ULTRA MAXIMUM UNLIMITED AI BETTING SYSTEM{Style.RESET_ALL}")
        print(f"{Fore.GREEN}🚀 ALL PARAMETERS SET TO MAXIMUM{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💎 UNLIMITED BETTING + ARBITRAGE + ALL AI FEATURES{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🛡️ FULL CRASH PROTECTION AND AUTO-RECOVERY{Style.RESET_ALL}")

        # Auto-setup with ULTRA HIGH bankroll for unlimited betting
        ultra_bankroll = 1000000  # 1 MILLION for truly unlimited betting
        bot.setup_ultra_max_strategies(ultra_bankroll)

        print(f"\n{Fore.RED}🚀 READY FOR ULTRA MAXIMUM UNLIMITED BETTING!{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚡ ALL systems optimized for maximum performance{Style.RESET_ALL}")
        input(f"{Fore.GREEN}Press Enter to begin ULTRA MAXIMUM unlimited betting...{Style.RESET_ALL}")

        # Start ULTRA unlimited betting
        bot.run_ultra_max_unlimited_betting()

    except Exception as e:
        print(f"{Fore.RED}💥 Critical startup error: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🛡️ Auto-recovery attempting restart...{Style.RESET_ALL}")
        time.sleep(5)
        # Attempt restart
        try:
            main()
        except:
            print(f"{Fore.RED}💥 Recovery failed - manual intervention required{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
