{"('heads', 'heads')": {"heads": 6, "tails": 2, "t": 1}, "('heads', 'heads', 'heads')": {"tails": 2, "heads": 3, "t": 1}, "('heads', 'tails')": {"tails": 2}, "('heads', 'heads', 'tails')": {"tails": 2}, "('heads', 'heads', 'heads', 'tails')": {"tails": 2}, "('tails', 'tails')": {"tails": 1, "heads": 1, "t": 1}, "('heads', 'tails', 'tails')": {"tails": 1, "t": 1}, "('heads', 'heads', 'tails', 'tails')": {"tails": 1, "t": 1}, "('heads', 'heads', 'heads', 'tails', 'tails')": {"tails": 1, "t": 1}, "('tails', 'tails', 'tails')": {"heads": 1}, "('heads', 'tails', 'tails', 'tails')": {"heads": 1}, "('heads', 'heads', 'tails', 'tails', 'tails')": {"heads": 1}, "('heads', 'heads', 'heads', 'tails', 'tails', 'tails')": {"heads": 1}, "('tails', 'heads')": {"heads": 2}, "('tails', 'tails', 'heads')": {"heads": 1}, "('tails', 'tails', 'tails', 'heads')": {"heads": 1}, "('heads', 'tails', 'tails', 'tails', 'heads')": {"heads": 1}, "('heads', 'heads', 'tails', 'tails', 'tails', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads', 'tails', 'tails', 'tails', 'heads')": {"heads": 1}, "('tails', 'heads', 'heads')": {"heads": 2}, "('tails', 'tails', 'heads', 'heads')": {"heads": 1}, "('tails', 'tails', 'tails', 'heads', 'heads')": {"heads": 1}, "('heads', 'tails', 'tails', 'tails', 'heads', 'heads')": {"heads": 1}, "('heads', 'heads', 'tails', 'tails', 'tails', 'heads', 'heads')": {"heads": 1}, "('tails', 'heads', 'heads', 'heads')": {"heads": 2}, "('tails', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('tails', 'tails', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('heads', 'tails', 'tails', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads', 'heads')": {"heads": 1, "tails": 1, "t": 1}, "('tails', 'heads', 'heads', 'heads', 'heads')": {"heads": 1, "t": 1}, "('tails', 'tails', 'heads', 'heads', 'heads', 'heads')": {"heads": 1}, "('tails', 'tails', 'tails', 'heads', 'heads', 'heads', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads', 'heads', 'heads')": {"tails": 1}, "('tails', 'heads', 'heads', 'heads', 'heads', 'heads')": {"tails": 1}, "('tails', 'tails', 'heads', 'heads', 'heads', 'heads', 'heads')": {"tails": 1}, "('heads', 'heads', 'heads', 'heads', 'tails')": {"tails": 1}, "('heads', 'heads', 'heads', 'heads', 'heads', 'tails')": {"tails": 1}, "('tails', 'heads', 'heads', 'heads', 'heads', 'heads', 'tails')": {"tails": 1}, "('heads', 'heads', 'heads', 'heads', 'tails', 'tails')": {"t": 1}, "('heads', 'heads', 'heads', 'heads', 'heads', 'tails', 'tails')": {"t": 1}, "('tails', 't')": {"tails": 1}, "('tails', 'tails', 't')": {"tails": 1}, "('heads', 'tails', 'tails', 't')": {"tails": 1}, "('heads', 'heads', 'tails', 'tails', 't')": {"tails": 1}, "('heads', 'heads', 'heads', 'tails', 'tails', 't')": {"tails": 1}, "('heads', 'heads', 'heads', 'heads', 'tails', 'tails', 't')": {"tails": 1}, "('t', 'tails')": {"heads": 1}, "('tails', 't', 'tails')": {"heads": 1}, "('tails', 'tails', 't', 'tails')": {"heads": 1}, "('heads', 'tails', 'tails', 't', 'tails')": {"heads": 1}, "('heads', 'heads', 'tails', 'tails', 't', 'tails')": {"heads": 1}, "('heads', 'heads', 'heads', 'tails', 'tails', 't', 'tails')": {"heads": 1}, "('t', 'tails', 'heads')": {"heads": 1}, "('tails', 't', 'tails', 'heads')": {"heads": 1}, "('tails', 'tails', 't', 'tails', 'heads')": {"heads": 1}, "('heads', 'tails', 'tails', 't', 'tails', 'heads')": {"heads": 1}, "('heads', 'heads', 'tails', 'tails', 't', 'tails', 'heads')": {"heads": 1}, "('t', 'tails', 'heads', 'heads')": {"heads": 1}, "('tails', 't', 'tails', 'heads', 'heads')": {"heads": 1}, "('tails', 'tails', 't', 'tails', 'heads', 'heads')": {"heads": 1}, "('heads', 'tails', 'tails', 't', 'tails', 'heads', 'heads')": {"heads": 1}, "('t', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('tails', 't', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('tails', 'tails', 't', 'tails', 'heads', 'heads', 'heads')": {"heads": 1}, "('t', 'tails', 'heads', 'heads', 'heads', 'heads')": {"t": 1}, "('tails', 't', 'tails', 'heads', 'heads', 'heads', 'heads')": {"t": 1}}