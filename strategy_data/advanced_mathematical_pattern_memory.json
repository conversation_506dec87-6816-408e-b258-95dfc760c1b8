{"('tails', 'heads')": {"t": 1, "heads": 1, "tails": 1}, "('heads', 't')": {"tails": 1, "t": 1}, "('tails', 'heads', 't')": {"tails": 1}, "('t', 'tails')": {"h": 1, "heads": 1}, "('heads', 't', 'tails')": {"h": 1}, "('tails', 'heads', 't', 'tails')": {"h": 1}, "('tails', 'h')": {"heads": 1}, "('t', 'tails', 'h')": {"heads": 1}, "('heads', 't', 'tails', 'h')": {"heads": 1}, "('tails', 'heads', 't', 'tails', 'h')": {"heads": 1}, "('h', 'heads')": {"heads": 1}, "('tails', 'h', 'heads')": {"heads": 1}, "('t', 'tails', 'h', 'heads')": {"heads": 1}, "('heads', 't', 'tails', 'h', 'heads')": {"heads": 1}, "('tails', 'heads', 't', 'tails', 'h', 'heads')": {"heads": 1}, "('heads', 'heads')": {"heads": 2, "t": 1, "h": 1}, "('h', 'heads', 'heads')": {"heads": 1}, "('tails', 'h', 'heads', 'heads')": {"heads": 1}, "('t', 'tails', 'h', 'heads', 'heads')": {"heads": 1}, "('heads', 't', 'tails', 'h', 'heads', 'heads')": {"heads": 1}, "('tails', 'heads', 't', 'tails', 'h', 'heads', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads')": {"heads": 1, "t": 1}, "('h', 'heads', 'heads', 'heads')": {"heads": 1}, "('tails', 'h', 'heads', 'heads', 'heads')": {"heads": 1}, "('t', 'tails', 'h', 'heads', 'heads', 'heads')": {"heads": 1}, "('heads', 't', 'tails', 'h', 'heads', 'heads', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads', 'heads')": {"t": 1}, "('h', 'heads', 'heads', 'heads', 'heads')": {"t": 1}, "('tails', 'h', 'heads', 'heads', 'heads', 'heads')": {"t": 1}, "('t', 'tails', 'h', 'heads', 'heads', 'heads', 'heads')": {"t": 1}, "('heads', 'heads', 't')": {"t": 1}, "('heads', 'heads', 'heads', 't')": {"t": 1}, "('heads', 'heads', 'heads', 'heads', 't')": {"t": 1}, "('h', 'heads', 'heads', 'heads', 'heads', 't')": {"t": 1}, "('tails', 'h', 'heads', 'heads', 'heads', 'heads', 't')": {"t": 1}, "('t', 't')": {"tails": 1}, "('heads', 't', 't')": {"tails": 1}, "('heads', 'heads', 't', 't')": {"tails": 1}, "('heads', 'heads', 'heads', 't', 't')": {"tails": 1}, "('heads', 'heads', 'heads', 'heads', 't', 't')": {"tails": 1}, "('h', 'heads', 'heads', 'heads', 'heads', 't', 't')": {"tails": 1}, "('t', 't', 'tails')": {"heads": 1}, "('heads', 't', 't', 'tails')": {"heads": 1}, "('heads', 'heads', 't', 't', 'tails')": {"heads": 1}, "('heads', 'heads', 'heads', 't', 't', 'tails')": {"heads": 1}, "('heads', 'heads', 'heads', 'heads', 't', 't', 'tails')": {"heads": 1}, "('t', 'tails', 'heads')": {"heads": 1}, "('t', 't', 'tails', 'heads')": {"heads": 1}, "('heads', 't', 't', 'tails', 'heads')": {"heads": 1}, "('heads', 'heads', 't', 't', 'tails', 'heads')": {"heads": 1}, "('heads', 'heads', 'heads', 't', 't', 'tails', 'heads')": {"heads": 1}, "('tails', 'heads', 'heads')": {"h": 1}, "('t', 'tails', 'heads', 'heads')": {"h": 1}, "('t', 't', 'tails', 'heads', 'heads')": {"h": 1}, "('heads', 't', 't', 'tails', 'heads', 'heads')": {"h": 1}, "('heads', 'heads', 't', 't', 'tails', 'heads', 'heads')": {"h": 1}, "('heads', 'h')": {"t": 1}, "('heads', 'heads', 'h')": {"t": 1}, "('tails', 'heads', 'heads', 'h')": {"t": 1}, "('t', 'tails', 'heads', 'heads', 'h')": {"t": 1}, "('t', 't', 'tails', 'heads', 'heads', 'h')": {"t": 1}, "('heads', 't', 't', 'tails', 'heads', 'heads', 'h')": {"t": 1}, "('h', 't')": {"heads": 1}, "('heads', 'h', 't')": {"heads": 1}, "('heads', 'heads', 'h', 't')": {"heads": 1}, "('tails', 'heads', 'heads', 'h', 't')": {"heads": 1}, "('t', 'tails', 'heads', 'heads', 'h', 't')": {"heads": 1}, "('t', 't', 'tails', 'heads', 'heads', 'h', 't')": {"heads": 1}, "('t', 'heads')": {"tails": 1}, "('h', 't', 'heads')": {"tails": 1}, "('heads', 'h', 't', 'heads')": {"tails": 1}, "('heads', 'heads', 'h', 't', 'heads')": {"tails": 1}, "('tails', 'heads', 'heads', 'h', 't', 'heads')": {"tails": 1}, "('t', 'tails', 'heads', 'heads', 'h', 't', 'heads')": {"tails": 1}, "('heads', 'tails')": {"heads": 1}, "('t', 'heads', 'tails')": {"heads": 1}, "('h', 't', 'heads', 'tails')": {"heads": 1}, "('heads', 'h', 't', 'heads', 'tails')": {"heads": 1}, "('heads', 'heads', 'h', 't', 'heads', 'tails')": {"heads": 1}, "('tails', 'heads', 'heads', 'h', 't', 'heads', 'tails')": {"heads": 1}, "('heads', 'tails', 'heads')": {"tails": 1}, "('t', 'heads', 'tails', 'heads')": {"tails": 1}, "('h', 't', 'heads', 'tails', 'heads')": {"tails": 1}, "('heads', 'h', 't', 'heads', 'tails', 'heads')": {"tails": 1}, "('heads', 'heads', 'h', 't', 'heads', 'tails', 'heads')": {"tails": 1}}