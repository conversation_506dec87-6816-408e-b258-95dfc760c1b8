# 🔄 RESTART GUIDE FOR UNLIMITED AI BETTING

## 🛑 When System Auto-Stops

The system will automatically stop for safety reasons:
- ✅ **10 consecutive losses** (primary safety)
- ✅ **Low bankroll** (below threshold)
- ✅ **Poor performance** (win rate < 35%)
- ✅ **AI strategy failure**

## 🚀 How to Restart for Continuous Play

### Method 1: Quick Restart Script (RECOMMENDED)
```bash
python3 restart_unlimited.py
```
**Features:**
- ✅ Automatic restart with learning data
- ✅ Wait timer options
- ✅ Error handling
- ✅ Manual restart control

### Method 2: Direct Restart
```bash
python3 run_advanced_unlimited.py
```
**Features:**
- ✅ Immediate restart
- ✅ All learning data preserved
- ✅ AI continues from where it stopped

### Method 3: Interactive Mode
```bash
python3 launch.py
```
**Features:**
- ✅ Choose restart options
- ✅ Switch between modes
- ✅ Full control

### Method 4: Original Interface
```bash
python3 main.py
```
**Features:**
- ✅ Manual strategy selection
- ✅ Custom settings
- ✅ Step-by-step control

## 🧠 Learning Data Persistence

### What Gets Saved Automatically:
- ✅ **Pattern Memory** - All discovered patterns
- ✅ **Markov Chains** - Sequence analysis data
- ✅ **Time Patterns** - Owo timing analysis
- ✅ **Win/Loss History** - Complete betting history
- ✅ **Strategy Performance** - AI learning parameters

### Where Data is Stored:
```
strategy_data/
├── advanced_mathematical/
│   ├── result_history.json
│   ├── pattern_memory.json
│   ├── transition_matrix.json
│   └── learning_params.json
└── owo_pattern_analyzer/
    ├── owo_results.json
    ├── time_patterns.json
    └── learning_params.json
```

## 🎯 Continuous Operation Strategies

### 1. Automatic Restart Loop
```bash
while true; do
    python3 run_advanced_unlimited.py
    echo "System stopped, restarting in 60 seconds..."
    sleep 60
done
```

### 2. Scheduled Restarts
```bash
# Add to crontab for hourly restarts
0 * * * * cd /path/to/bot && python3 run_advanced_unlimited.py
```

### 3. Monitoring Script
```bash
python3 restart_unlimited.py  # Handles everything automatically
```

## ⚡ Quick Commands Reference

| Command | Purpose |
|---------|---------|
| `python3 restart_unlimited.py` | **Auto-restart with options** |
| `python3 run_advanced_unlimited.py` | **Direct unlimited betting** |
| `python3 launch.py` | **Interactive launcher** |
| `python3 main.py` | **Original interface** |

## 🔧 Advanced Restart Options

### Modify Restart Behavior:
Edit `run_advanced_unlimited.py` to change:
- **Bankroll amount** (line 285: `bankroll = 100000`)
- **Safety thresholds** (config.py)
- **AI aggressiveness** (strategy parameters)

### Custom Restart Script:
```python
import subprocess
import time

while True:
    try:
        subprocess.run(["python3", "run_advanced_unlimited.py"])
        time.sleep(300)  # Wait 5 minutes before restart
    except KeyboardInterrupt:
        break
```

## 🛡️ Safety Features

### Built-in Protections:
- ✅ **10 loss limit** - Prevents major losses
- ✅ **Bankroll protection** - Stops at low balance
- ✅ **Performance monitoring** - Stops if AI fails
- ✅ **Rate limiting** - Prevents owo timeouts

### Manual Override:
- **Ctrl+C** - Stop anytime
- **Safety stops** - Can be adjusted in config.py
- **Learning data** - Always preserved

## 📊 Monitoring Continuous Operation

### Check Learning Progress:
```bash
ls -la strategy_data/  # View saved learning files
```

### View Current Status:
- Watch terminal output for real-time stats
- Learning data grows with each session
- AI becomes smarter over time

## 🎯 Best Practices for Continuous Play

1. **Use restart_unlimited.py** - Handles everything automatically
2. **Monitor first few restarts** - Ensure everything works
3. **Check learning data growth** - Verify AI is improving
4. **Adjust safety limits** if needed - Based on your risk tolerance
5. **Let it run overnight** - AI learns best with continuous data

## 🚀 Ready to Restart?

Choose your preferred method:
- **Easiest**: `python3 restart_unlimited.py`
- **Direct**: `python3 run_advanced_unlimited.py`
- **Interactive**: `python3 launch.py`

The AI will automatically load all previous learning data and continue improving! 🧠✨
