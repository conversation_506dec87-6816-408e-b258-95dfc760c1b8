#!/usr/bin/env python3
"""
FORCE CONTINUE - Ignores all safety stops and continues betting
- Overrides performance-based stops
- Continues betting regardless of win rate
- Maximum aggression mode
"""

import subprocess
import sys
import time
from colorama import init, Fore, Style

init()

def force_continue_betting():
    """Force the system to continue betting by overriding safety mechanisms"""
    
    print(f"{Fore.RED}🚀 FORCE CONTINUE MODE{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚠️ This will override ALL safety stops{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚠️ System will continue betting regardless of performance{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ All learning data will be preserved{Style.RESET_ALL}")
    
    confirm = input(f"\n{Fore.RED}Type 'FORCE' to continue betting regardless of safety stops: {Style.RESET_ALL}").strip()
    
    if confirm.upper() == "FORCE":
        print(f"\n{Fore.RED}🚀 FORCING SYSTEM TO CONTINUE BETTING...{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚡ Overriding all safety mechanisms{Style.RESET_ALL}")
        
        # Create a modified version that ignores safety stops
        with open("PERFECT_MAX.py", "r") as f:
            content = f.read()
        
        # Modify the safety check to always return False (never stop)
        modified_content = content.replace(
            "if self.active_strategy.should_stop_betting():",
            "if False:  # FORCE CONTINUE - Safety stops disabled"
        )
        
        # Also disable performance-based stops
        modified_content = modified_content.replace(
            "current_performance < 0.30",
            "current_performance < 0.01"  # Only stop at 1% win rate
        )
        
        # Save the modified version
        with open("FORCE_MAX.py", "w") as f:
            f.write(modified_content)
        
        print(f"{Fore.GREEN}✅ Created FORCE_MAX.py with safety stops disabled{Style.RESET_ALL}")
        print(f"{Fore.RED}🚀 Launching forced continuation...{Style.RESET_ALL}")
        
        # Launch the forced version
        subprocess.run([sys.executable, "FORCE_MAX.py"])
        
    else:
        print(f"{Fore.GREEN}👋 Cancelled. Use normal restart instead.{Style.RESET_ALL}")

if __name__ == "__main__":
    force_continue_betting()
